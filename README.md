# Albatross KiotViet MCP Server

A Model Context Protocol (MCP) server for integrating with KiotViet Public API. This server allows you to interact with KiotViet's retail management system through AI tools, enabling natural language queries for product categories, inventory, and more.

## Features

- **🏗️ Clean Architecture**: Implements clean architecture principles with clear separation of concerns
- **🔐 Secure Authentication**: Automatic token management with refresh handling
- **🛠️ Multiple Tools**: Support for categories, products, branches, and customers
- **🧪 Comprehensive Testing**: Full test suite with 70% coverage (52 tests)
- **🔒 Type Safety**: Complete type hints throughout the codebase
- **⚡ Rate Limiting**: Built-in request throttling and retry logic
- **🚀 Production Ready**: Error handling, logging, and containerization
- **🔧 Extensible**: Easy to add new KiotViet API endpoints
- **🐳 Containerized**: Docker support for easy deployment
- **📚 Well Documented**: Comprehensive documentation and examples

## Prerequisites

- **Python 3.10 or higher**
- **KiotViet Developer Account**: You need API credentials from KiotViet
  - Client ID
  - Client Secret  
  - Retailer name
- **FastMCP**: For MCP integration
- Optional: **Claude Desktop** for AI integration

## Installation

### Option 1: Install from Source

1. **Clone the Repository**:
   ```bash
   git clone <repository-url>
   cd albatross-kiotviet-mcp
   ```

2. **Set Up Virtual Environment**:
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install Dependencies**:
   ```bash
   pip install -e .
   ```

4. **Configure Environment Variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your KiotViet credentials
   ```

### Option 2: Install with uv (Recommended)

```bash
uv sync
```

### Option 3: Docker (Production)

```bash
# Build the Docker image
docker build -t albatross-kiotviet-mcp .

# Run with Docker Compose
docker-compose up -d

# Or run directly with Docker
docker run -d \
  --name albatross-kiotviet-mcp \
  -e KIOTVIET_CLIENT_ID=your_client_id \
  -e KIOTVIET_CLIENT_SECRET=your_client_secret \
  -e KIOTVIET_RETAILER=your_retailer \
  -v $(pwd)/logs:/app/logs \
  albatross-kiotviet-mcp
```

## Configuration

Create a `.env` file in the project root with your KiotViet API credentials:

```env
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_name_here
```

### Optional Configuration

```env
# API endpoints (defaults shown)
KIOTVIET_AUTH_URL=https://id.kiotviet.vn/connect/token
KIOTVIET_API_BASE_URL=https://public.kiotapi.com

# Token and request settings
KIOTVIET_TOKEN_BUFFER_SECONDS=300  # Refresh token 5 min before expiry
KIOTVIET_REQUEST_TIMEOUT=30        # Request timeout in seconds
KIOTVIET_MAX_RETRIES=3             # Max retry attempts
```

## Running the Server

### Using the CLI Script

```bash
albatross-kiotviet-mcp-server
```

### Using uv

```bash
uv run albatross-kiotviet-mcp-server
```

### Development Mode

```bash
fastmcp dev src/albatross_kiotviet_mcp/server.py
```

## Using with Claude Desktop

### Step 1: Configure Claude Desktop

Edit your Claude Desktop configuration file:

- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

### Step 2: Add Server Configuration

```json
{
  "mcpServers": {
    "kiotviet": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/albatross-kiotviet-mcp",
        "run",
        "albatross-kiotviet-mcp-server"
      ],
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

### Step 3: Restart Claude Desktop

Quit and reopen Claude Desktop to load the new configuration.

## Available Tools

### `get_categories`

Retrieve product categories from KiotViet with pagination support.

**Parameters:**
- `page_size` (optional): Number of items per page (default: 50, max: 100)
- `current_item` (optional): Starting item index for pagination (default: 0)
- `order_direction` (optional): Sort order - "Asc" or "Desc" (default: "Asc")
- `hierarchical_data` (optional): Whether to return hierarchical structure (default: False)

### `get_products`

Retrieve products from KiotViet with pagination and filtering support.

**Parameters:**
- `page_size` (optional): Number of items per page (default: 50, max: 100)
- `current_item` (optional): Starting item index for pagination (default: 0)
- `order_direction` (optional): Sort order - "Asc" or "Desc" (default: "Asc")
- `category_id` (optional): Filter by category ID
- `include_inventory` (optional): Include inventory information (default: False)

### `get_branches`

Retrieve branches from KiotViet with pagination support.

**Parameters:**
- `page_size` (optional): Number of items per page (default: 50, max: 100)
- `current_item` (optional): Starting item index for pagination (default: 0)
- `order_direction` (optional): Sort order - "Asc" or "Desc" (default: "Asc")

**Claude Desktop Examples:**

```
Get all product categories from KiotViet
```

```
Show me the first 20 products in Electronics category
```

```
Get all branches with their details
```

## Architecture

The server is built with a clean, modular architecture following clean architecture principles:

```
src/albatross_kiotviet_mcp/
├── __init__.py                    # Package initialization
├── main.py                        # Application entry point
├── server.py                      # FastMCP server setup
├── domain/                        # Business logic and entities
│   ├── entities/                  # Domain entities (Category, Product, etc.)
│   └── interfaces/                # Abstract interfaces
├── infrastructure/                # External concerns
│   ├── api/                       # KiotViet API client implementation
│   ├── auth/                      # Authentication management
│   └── config/                    # Configuration management
├── application/                   # Use cases and services
│   └── services/                  # Business services
└── tools/                         # Individual MCP tools
    ├── base/                      # Base tool classes
    ├── categories/                # Categories tool
    ├── products/                  # Products tool
    └── branches/                  # Branches tool
```

### Key Components

- **Domain Layer**: Contains business entities and interfaces
- **Infrastructure Layer**: Handles external concerns (API, auth, config)
- **Application Layer**: Contains business services and use cases
- **Tools Layer**: Individual MCP tools with their own responsibilities
- **Clean Separation**: Each layer has clear responsibilities and dependencies

### Clean Architecture Benefits

- ✅ **Separation of Concerns**: Each layer has a single responsibility
- ✅ **Dependency Inversion**: High-level modules don't depend on low-level modules
- ✅ **Testability**: Easy to mock dependencies and test individual components (70% coverage)
- ✅ **Maintainability**: Changes in one layer don't affect others
- ✅ **Extensibility**: Easy to add new tools and features
- ✅ **Type Safety**: Complete type hints throughout the codebase

## Development

### Running Tests

```bash
# Install test dependencies
uv sync --extra test

# Run all tests
pytest

# Run tests with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/test_categories_tool.py

# Run tests in verbose mode
pytest -v
```

### Code Quality

```bash
# Install development dependencies
uv sync --extra dev

# Format code
black src tests

# Sort imports
isort src tests

# Lint code
flake8 src tests

# Type checking
mypy src
```

### Adding New API Endpoints

The codebase is designed for easy extension. To add a new KiotViet API endpoint:

1. **Add method to `KiotVietAPIClient`**:
   ```python
   async def get_customers(self, **kwargs) -> Dict[str, Any]:
       return await self.make_request("GET", "/customers", data=kwargs)
   ```

2. **Create a new tool**:
   ```python
   # In tools/customers/customers_tool.py
   class CustomersTool(BaseMCPTool):
       async def execute(self, **kwargs) -> Dict[str, Any]:
           # Implementation here
   ```

3. **Add tool to server**:
   ```python
   @server.tool(name="get_customers", description="Get customers from KiotViet")
   async def get_customers(**kwargs) -> Dict[str, Any]:
       tool = get_customers_tool()
       return await tool.execute(**kwargs)
   ```

4. **Write tests**:
   ```python
   # In tests/test_customers_tool.py
   class TestCustomersTool:
       # Test cases here
   ```

## Error Handling

The server includes comprehensive error handling:

- **Authentication errors**: Automatic token refresh on 401 responses
- **Network errors**: Retry logic with exponential backoff
- **Configuration errors**: Validation on startup
- **API errors**: Detailed error messages and logging

## Troubleshooting

### Server Not Starting

- Check your `.env` file has all required credentials
- Verify KiotViet API credentials are correct
- Check logs for specific error messages

### Authentication Errors

- Verify `KIOTVIET_CLIENT_ID` and `KIOTVIET_CLIENT_SECRET` are correct
- Ensure your KiotViet account has API access enabled
- Check if `KIOTVIET_RETAILER` name matches your account

### Claude Desktop Integration

- Verify the path in `claude_desktop_config.json` is correct
- Restart Claude Desktop after configuration changes
- Check Claude's MCP logs for connection errors

## Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
- Check the troubleshooting section
- Review KiotViet API documentation
- Open an issue on GitHub