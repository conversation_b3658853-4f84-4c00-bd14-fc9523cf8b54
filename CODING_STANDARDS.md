# KiotViet MCP Project - Coding Standards & Guidelines

## Project Overview

This is a **Model Context Protocol (MCP) server** for integrating with KiotViet Public API, built with **Clean Architecture** principles. The project enables AI assistants like <PERSON> to interact with KiotViet's retail management system through natural language queries.

## Architecture Principles

### Clean Architecture Layers

```
Domain Layer (Core Business Logic)
├── entities/          # Pydantic models for business entities
└── interfaces/        # Abstract interfaces (dependency inversion)

Infrastructure Layer (External Concerns)
├── api/              # HTTP clients and API integrations
├── auth/             # Authentication and token management
└── config/           # Configuration and environment settings

Tools Layer (MCP-Specific)
├── base/             # Abstract base classes for tools
└── {tool_name}/      # Individual MCP tool implementations

Application Layer (Use Cases)
└── services/         # Business services and orchestration
```

### Dependency Flow Rules

1. **Domain Layer**: No dependencies on other layers (pure business logic)
2. **Infrastructure Layer**: Can depend on Domain interfaces only
3. **Tools Layer**: Can depend on Domain and Infrastructure
4. **Application Layer**: Orchestrates all layers

## Code Quality Standards

### 1. Type Safety (MANDATORY)

**All code must have complete type annotations:**

```python
# ✅ CORRECT - Complete type hints
async def get_categories(
    self,
    page_size: int = 50,
    current_item: int = 0,
    order_direction: str = "Asc"
) -> CategoryResponse:
    """Get categories with full type safety."""
    pass

# ❌ INCORRECT - Missing type hints
async def get_categories(self, page_size=50, current_item=0):
    pass
```

**Required imports for typing:**
```python
from typing import Dict, Any, Optional, List, Union, Callable
from abc import ABC, abstractmethod
```

### 2. Pydantic Model Conventions

**Domain entities must use Pydantic with proper configuration:**

```python
# ✅ CORRECT - Proper Pydantic entity
from pydantic import BaseModel, Field, ConfigDict

class Category(BaseModel):
    """KiotViet category entity."""
    
    # Use alias for API field mapping
    category_id: int = Field(alias="categoryId", description="Category ID")
    category_name: str = Field(alias="categoryName", description="Category name")
    retailer_id: Optional[int] = Field(alias="retailerId", default=None)
    
    # Required configuration
    model_config = ConfigDict(from_attributes=True)

class CategoryResponse(BaseModel):
    """API response wrapper."""
    
    total: int = Field(description="Total number of categories")
    page_size: int = Field(alias="pageSize", description="Items per page")
    data: List[Category] = Field(description="List of categories")
    
    model_config = ConfigDict(from_attributes=True)
```

### 3. Async/Await Patterns

**Always use async context managers for resources:**

```python
# ✅ CORRECT - Proper async context manager usage
async def execute(self) -> Dict[str, Any]:
    async with self.api_client:
        result = await self.api_client.get_categories()
    return result.model_dump()

# ❌ INCORRECT - Missing context manager
async def execute(self) -> Dict[str, Any]:
    result = await self.api_client.get_categories()  # Resource leak risk
    return result
```

### 4. Error Handling Standards

**Use specific exception types with proper logging:**

```python
# ✅ CORRECT - Specific exceptions with context
try:
    result = await self.api_client.get_categories()
except ValueError as e:
    self.logger.error(f"Invalid parameters: {e}")
    raise CustomToolError(f"Invalid parameters: {str(e)}")
except httpx.HTTPStatusError as e:
    self.logger.error(f"API error {e.response.status_code}: {e}")
    raise CustomToolError(f"API request failed: {str(e)}")

# ❌ INCORRECT - Generic exception handling
try:
    result = await self.api_client.get_categories()
except Exception as e:
    raise e  # Loses context and doesn't log
```

### 5. Import Organization

**Follow strict import ordering:**

```python
# 1. Standard library imports
import asyncio
import logging
from typing import Dict, Any, Optional

# 2. Third-party imports
import httpx
from pydantic import BaseModel, Field

# 3. Local imports (relative)
from ..base.base_tool import BaseMCPTool
from ...domain.interfaces.api_client import IKiotVietAPIClient
from ...domain.entities.category import CategoryResponse
```

## Architecture Patterns

### 1. MCP Tool Implementation Pattern

**Every MCP tool must follow this exact pattern:**

```python
class NewTool(BaseMCPTool):
    """MCP tool for [specific functionality]."""

    def __init__(self, api_client: IKiotVietAPIClient):
        super().__init__(
            name="tool_name",
            description="Clear description of what this tool does"
        )
        self.api_client = api_client
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with validation and error handling."""
        try:
            # 1. Validate parameters
            self.pre_execute(**kwargs)
            
            # 2. Log operation
            self.logger.info(f"Executing {self.name} with params: {kwargs}")
            
            # 3. Make API call with context manager
            async with self.api_client:
                typed_result = await self.api_client.method_name(**kwargs)
            
            # 4. Return serialized result for FastMCP
            return typed_result.model_dump()
            
        except ValueError as e:
            self.logger.error(f"Parameter validation failed: {e}")
            raise CustomToolError(f"Invalid parameters: {str(e)}")
        except Exception as e:
            self.logger.error(f"Tool execution failed: {e}")
            raise CustomToolError(f"Failed to execute {self.name}: {str(e)}")
    
    def pre_execute(self, **kwargs) -> None:
        """Validate all parameters with specific error messages."""
        # Implement parameter validation with clear error messages
        pass
    
    def _create_wrapper_function(self) -> Callable:
        """Create FastMCP wrapper with exact parameter signature."""
        async def wrapper_name(
            param1: type = default,
            param2: type = default
        ) -> Dict[str, Any]:
            """Docstring matching the tool description."""
            return await self.execute(param1=param1, param2=param2)
        
        return wrapper_name
```

### 2. API Client Pattern

**API client methods must return typed responses:**

```python
async def get_resource(
    self,
    page_size: int = 50,
    current_item: int = 0
) -> ResourceResponse:
    """Get resource with typed response."""
    data = {
        "pageSize": page_size,
        "currentItem": current_item
    }
    
    raw_response = await self.make_request("GET", "/endpoint", data=data)
    return ResourceResponse.model_validate(raw_response)
```

### 3. Configuration Pattern

**Use Pydantic Settings for all configuration:**

```python
class NewConfig(BaseSettings):
    """Configuration for new feature."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8"
    )
    
    # Required fields
    required_field: str = Field(alias="ENV_VAR_NAME")
    
    # Optional fields with defaults
    optional_field: int = Field(default=30, alias="ENV_VAR_OPTIONAL")
```

## File Organization Rules

### 1. Module Structure

```
src/albatross_kiotviet_mcp/
├── domain/
│   ├── entities/
│   │   └── {entity_name}.py      # One entity per file
│   └── interfaces/
│       └── {interface_name}.py   # One interface per file
├── infrastructure/
│   ├── api/
│   │   ├── base_client.py        # Shared base implementation
│   │   └── kiotviet_client.py    # Concrete implementation
│   ├── auth/
│   │   └── token_manager.py      # Authentication logic
│   └── config/
│       └── settings.py           # All configuration classes
├── tools/
│   ├── base/
│   │   └── base_tool.py          # Abstract base class
│   └── {tool_name}/
│       └── {tool_name}_tool.py   # One tool per directory
└── application/
    └── services/
        └── {service_name}.py     # Business services
```

### 2. Naming Conventions

- **Files**: `snake_case.py`
- **Classes**: `PascalCase`
- **Functions/Methods**: `snake_case`
- **Constants**: `UPPER_SNAKE_CASE`
- **Private methods**: `_private_method`
- **Abstract methods**: `abstractmethod` decorator

### 3. Documentation Requirements

**Every module, class, and public method must have docstrings:**

```python
"""Module-level docstring describing the module's purpose."""

class ExampleClass:
    """Class docstring explaining the class purpose and usage."""
    
    def public_method(self, param: str) -> str:
        """Method docstring with Args and Returns sections.
        
        Args:
            param: Description of the parameter
            
        Returns:
            Description of the return value
            
        Raises:
            ValueError: When parameter is invalid
        """
        pass
```

## Testing Standards

### 1. Test Organization

```
tests/
├── conftest.py                   # Shared fixtures
├── test_{tool_name}_tool.py      # Unit tests (with mocks)
└── integration/
    └── test_{tool_name}_integration.py  # Integration tests (real API)
```

### 2. Unit Test Pattern

```python
class TestToolName:
    """Unit tests for ToolName (with mocks)."""
    
    @pytest.fixture
    def tool_instance(self, mock_api_client):
        """Create tool instance with mocked dependencies."""
        return ToolName(mock_api_client)
    
    @pytest.mark.asyncio
    async def test_execute_success(self, tool_instance, mock_api_client):
        """Test successful execution with mocked API."""
        # Arrange
        mock_response = SampleResponse(data=[], total=0)
        mock_api_client.method_name.return_value = mock_response
        
        # Act
        result = await tool_instance.execute(param=value)
        
        # Assert
        assert result == mock_response.model_dump()
        mock_api_client.method_name.assert_called_once_with(param=value)
```

### 3. Integration Test Pattern

```python
class TestToolNameIntegration:
    """Integration tests for ToolName (real API calls)."""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_api_call(self):
        """Test with real KiotViet API."""
        # Use real configuration and API client
        config = get_config()
        api_client = KiotVietAPIClient(config)
        tool = ToolName(api_client)
        
        # Make real API call
        result = await tool.execute(param=value)
        
        # Validate response structure
        assert isinstance(result, dict)
        assert "data" in result
```

## Environment & Dependencies

### 1. Dependency Management

- **Use `uv` for dependency management** (modern, fast)
- **Pin major versions** in `pyproject.toml`
- **Use dependency groups** for test/dev dependencies

### 2. Environment Variables

**All configuration through environment variables:**

```env
# Required
KIOTVIET_CLIENT_ID=your_client_id
KIOTVIET_CLIENT_SECRET=your_client_secret
KIOTVIET_RETAILER=your_retailer

# Optional (with defaults)
KIOTVIET_REQUEST_TIMEOUT=30
KIOTVIET_MAX_RETRIES=3
```

### 3. Logging Configuration

**Use structured logging throughout:**

```python
import logging

logger = logging.getLogger(__name__)

# Log levels:
logger.debug("Detailed debugging information")
logger.info("General information about operation")
logger.warning("Warning about potential issues")
logger.error("Error that needs attention")
```

## Code Review Checklist

Before submitting code, ensure:

- [ ] **Complete type hints** on all functions/methods
- [ ] **Pydantic models** for all data structures
- [ ] **Async context managers** for all resources
- [ ] **Specific exception handling** with logging
- [ ] **Proper import organization** (stdlib, third-party, local)
- [ ] **Comprehensive docstrings** on public APIs
- [ ] **Unit tests** with mocks for fast feedback
- [ ] **Integration tests** for real API validation
- [ ] **Clean Architecture** layer separation respected
- [ ] **No circular imports** or tight coupling

## Performance Guidelines

1. **Use async/await** throughout for I/O operations
2. **Implement connection pooling** in HTTP clients
3. **Add retry logic** with exponential backoff
4. **Use Pydantic validation** for data integrity
5. **Implement proper resource cleanup** with context managers
6. **Log performance metrics** for monitoring

This document serves as the definitive guide for maintaining code quality and architectural consistency in the KiotViet MCP project.
