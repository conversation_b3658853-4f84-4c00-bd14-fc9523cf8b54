#!/usr/bin/env python3
"""
Debug script to test real API calls step by step.
NO MOCKS - Real integration testing!
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, 'src')

async def test_categories_tool_real_api():
    """Test categories tool with real API calls - step by step debugging."""
    
    print("🚀 REAL API INTEGRATION TEST - NO MOCKS!")
    print("=" * 60)
    
    try:
        # Step 1: Import and load config
        print("\n📋 Step 1: Loading configuration...")
        from albatross_kiotviet_mcp.infrastructure.config.settings import get_config
        
        config = get_config()
        print(f"✅ Config loaded successfully!")
        print(f"   Retailer: {config.retailer}")
        print(f"   API Base URL: {config.api_base_url}")
        print(f"   Client ID: {config.client_id[:8]}...")
        print(f"   Request Timeout: {config.request_timeout}s")
        print(f"   Max Retries: {config.max_retries}")
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        print("💡 Make sure environment variables are set:")
        print("   - KIOTVIET_CLIENT_ID")
        print("   - KIOTVIET_CLIENT_SECRET") 
        print("   - KIOTVIET_RETAILER")
        return
    
    try:
        # Step 2: Create API client
        print("\n🔌 Step 2: Creating API client...")
        from albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
        
        api_client = KiotVietAPIClient(config)
        print("✅ API client created successfully!")
        
    except Exception as e:
        print(f"❌ API client creation failed: {e}")
        return
    
    try:
        # Step 3: Create categories tool
        print("\n🛠️ Step 3: Creating categories tool...")
        from albatross_kiotviet_mcp.tools.categories.categories_tool import CategoriesTool
        
        categories_tool = CategoriesTool(api_client)
        print(f"✅ Categories tool created!")
        print(f"   Tool name: {categories_tool.name}")
        print(f"   Tool description: {categories_tool.description[:50]}...")
        
    except Exception as e:
        print(f"❌ Categories tool creation failed: {e}")
        return
    
    try:
        # Step 4: Test wrapper function
        print("\n🔧 Step 4: Testing wrapper function...")
        wrapper_func = categories_tool.get_wrapper_function()
        print(f"✅ Wrapper function obtained: {wrapper_func.__name__}")
        print(f"   Function doc: {wrapper_func.__doc__[:50] if wrapper_func.__doc__ else 'No doc'}...")
        
    except Exception as e:
        print(f"❌ Wrapper function test failed: {e}")
        return
    
    try:
        # Step 5: Make REAL API call
        print("\n📡 Step 5: Making REAL API call...")
        print("   Parameters:")
        print("   - page_size: 5")
        print("   - current_item: 0") 
        print("   - order_direction: Asc")
        print("   - hierarchical_data: False")
        
        result = await categories_tool.execute(
            page_size=5,
            current_item=0,
            order_direction="Asc",
            hierarchical_data=False
        )
        
        print("🎉 API CALL SUCCESSFUL!")
        print(f"   Result type: {type(result)}")
        print(f"   Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
    except Exception as e:
        print(f"❌ API call failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        
        # Check if it's authentication error
        if "401" in str(e) or "authentication" in str(e).lower():
            print("💡 This looks like an authentication error.")
            print("   Check your API credentials in environment variables.")
        elif "timeout" in str(e).lower():
            print("💡 This looks like a timeout error.")
            print("   The API might be slow or unreachable.")
        elif "connection" in str(e).lower():
            print("💡 This looks like a connection error.")
            print("   Check your internet connection and API URL.")
        
        return
    
    try:
        # Step 6: Validate response structure
        print("\n🔍 Step 6: Validating response structure...")
        
        assert isinstance(result, dict), f"Expected dict, got {type(result)}"
        print("✅ Result is a dictionary")
        
        # Check actual API response fields (KiotViet uses different field names)
        expected_fields = {
            "data": "data",
            "total": "total",
            "page_size": "pageSize",  # KiotViet uses pageSize, not page_size
            "current_item": "currentItem"  # Might be currentItem
        }

        for logical_field, actual_field in expected_fields.items():
            if actual_field in result:
                print(f"✅ Field '{logical_field}' (as '{actual_field}') present: {result[actual_field]}")
            elif logical_field in result:
                print(f"✅ Field '{logical_field}' present: {result[logical_field]}")
            else:
                print(f"⚠️ Field '{logical_field}' missing (checked both '{actual_field}' and '{logical_field}')")
                print(f"   Available fields: {list(result.keys())}")
        
        print(f"\n📊 RESPONSE SUMMARY:")
        print(f"   Total categories: {result.get('total', 'unknown')}")
        print(f"   Returned categories: {len(result.get('data', []))}")
        print(f"   Page size: {result.get('pageSize', result.get('page_size', 'unknown'))}")
        print(f"   Current item: {result.get('currentItem', result.get('current_item', 'unknown'))}")
        
    except Exception as e:
        print(f"❌ Response validation failed: {e}")
        return
    
    try:
        # Step 7: Examine category data
        print("\n📋 Step 7: Examining category data...")
        
        categories = result.get('data', [])
        if categories:
            print(f"✅ Found {len(categories)} categories")
            
            first_category = categories[0]
            print(f"\n📝 First category details:")
            for key, value in first_category.items():
                print(f"   {key}: {value}")
            
            # Check required category fields
            required_cat_fields = ["id", "name"]
            for field in required_cat_fields:
                if field in first_category:
                    print(f"✅ Category has '{field}': {first_category[field]}")
                else:
                    print(f"⚠️ Category missing '{field}'")
        else:
            print("⚠️ No categories returned (might be empty or filtered)")
        
    except Exception as e:
        print(f"❌ Category data examination failed: {e}")
        return
    
    print("\n🎉 INTEGRATION TEST COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("✅ All steps passed - Real API integration working!")
    
    return result

async def test_wrapper_function():
    """Test the wrapper function that FastMCP actually calls."""
    
    print("\n🔧 TESTING WRAPPER FUNCTION (FastMCP simulation)")
    print("=" * 60)
    
    try:
        from albatross_kiotviet_mcp.infrastructure.config.settings import get_config
        from albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
        from albatross_kiotviet_mcp.tools.categories.categories_tool import CategoriesTool
        
        config = get_config()
        api_client = KiotVietAPIClient(config)
        categories_tool = CategoriesTool(api_client)
        
        # Get wrapper function (this is what FastMCP calls)
        wrapper_func = categories_tool.get_wrapper_function()
        
        print(f"📞 Calling wrapper function: {wrapper_func.__name__}")
        
        # Call wrapper function directly (simulating FastMCP)
        result = await wrapper_func(
            page_size=3,
            current_item=0,
            order_direction="Asc",
            hierarchical_data=False
        )
        
        print("✅ Wrapper function call successful!")
        print(f"   Result type: {type(result)}")
        print(f"   Categories returned: {len(result.get('data', []))}")
        
        return result
        
    except Exception as e:
        print(f"❌ Wrapper function test failed: {e}")
        return None

if __name__ == "__main__":
    print("🧪 REAL API INTEGRATION TESTING")
    print("This script will make REAL API calls to KiotViet!")
    print()
    
    # Run the tests
    result1 = asyncio.run(test_categories_tool_real_api())
    
    if result1:
        print("\n" + "="*60)
        result2 = asyncio.run(test_wrapper_function())
        
        if result2:
            print("\n🎊 ALL TESTS PASSED! Your integration is working perfectly!")
        else:
            print("\n⚠️ Wrapper function test failed, but main test passed.")
    else:
        print("\n❌ Main test failed. Check the errors above.")
