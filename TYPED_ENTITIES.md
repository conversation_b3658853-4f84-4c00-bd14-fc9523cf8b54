# Typed Entities Implementation

## Overview

This document explains the implementation of **typed entities** in the KiotViet MCP server, providing **perfect type safety** and **automatic validation** throughout the application.

## Why Typed Entities?

### Before: Raw Dictionaries
```python
# API Client
async def get_categories(...) -> Dict[str, Any]:
    return {"data": [...], "total": 123}  # No validation

# Tool
result = await client.get_categories()
name = result["data"][0]["name"]  # No type safety, runtime errors possible
```

**Problems:**
- ❌ No type safety
- ❌ No validation
- ❌ Runtime errors if API changes
- ❌ No IDE autocomplete
- ❌ Hard to debug

### After: Typed Entities
```python
# API Client
async def get_categories(...) -> CategoryResponse:
    raw_data = await self.make_request("GET", "/categories")
    return CategoryResponse.model_validate(raw_data)  # Automatic validation

# Tool
result = await client.get_categories()
name = result.data[0].name  # Perfect type safety + autocomplete
```

**Benefits:**
- ✅ Perfect type safety
- ✅ Automatic validation
- ✅ Catch API changes immediately
- ✅ Full IDE autocomplete
- ✅ Self-documenting code

## Implementation Architecture

### 1. Domain Entities

**Category Entity:**
```python
class Category(BaseModel):
    id: int = Field(description="Category ID")
    name: str = Field(description="Category name")
    description: Optional[str] = Field(default=None)
    parent_id: Optional[int] = Field(default=None)
    is_active: bool = Field(default=True)
    # ... more fields
```

**Response Wrapper:**
```python
class CategoryResponse(BaseModel):
    data: List[Category] = Field(description="List of categories")
    total: int = Field(description="Total number of categories")
    page_size: int = Field(description="Number of items per page")
    current_item: int = Field(description="Current item index")
```

### 2. API Client Layer

**Interface:**
```python
class IKiotVietAPIClient(ABC):
    @abstractmethod
    async def get_categories(...) -> CategoryResponse:
        """Returns typed, validated response."""
        pass
```

**Implementation:**
```python
class KiotVietAPIClient(BaseKiotVietClient):
    async def get_categories(...) -> CategoryResponse:
        raw_response = await self.make_request("GET", "/categories")
        return CategoryResponse.model_validate(raw_response)  # Validation!
```

### 3. Tool Layer

**Tools receive typed data but return JSON for FastMCP:**
```python
class CategoriesTool(BaseMCPTool):
    async def execute(...) -> Dict[str, Any]:
        # Get typed response
        typed_result = await self.api_client.get_categories(...)
        
        # Access with type safety
        self.logger.info(f"Retrieved {len(typed_result.data)} categories")
        
        # Convert to JSON for FastMCP
        return typed_result.model_dump()
```

## Benefits Achieved

### 1. **Type Safety**
```python
# Before: Runtime error possible
categories = result["data"]  # Could be None, wrong type, etc.

# After: Compile-time safety
categories = result.data  # Guaranteed to be List[Category]
```

### 2. **Automatic Validation**
```python
# Pydantic automatically validates:
# - Required fields are present
# - Types are correct (int, str, bool, etc.)
# - Field constraints are met
# - Nested objects are valid

# If API returns invalid data:
CategoryResponse.model_validate(bad_data)  # Raises ValidationError
```

### 3. **IDE Support**
```python
result = await client.get_categories()
result.  # IDE shows: data, total, page_size, current_item
result.data[0].  # IDE shows: id, name, description, parent_id, etc.
```

### 4. **Self-Documenting**
```python
class Category(BaseModel):
    id: int = Field(description="Category ID")  # Clear documentation
    name: str = Field(description="Category name")
    # Field descriptions become API documentation
```

### 5. **Error Handling**
```python
try:
    response = CategoryResponse.model_validate(api_data)
except ValidationError as e:
    # Detailed error about what's wrong with the data
    logger.error(f"API response validation failed: {e}")
```

## Entity Files

- **`domain/entities/category.py`**: Category and CategoryResponse
- **`domain/entities/product.py`**: Product and ProductResponse  
- **`domain/entities/branch.py`**: Branch and BranchResponse

## Flow Diagram

```
API Response (JSON) 
    ↓
Pydantic Validation
    ↓
Typed Entity (CategoryResponse)
    ↓
Business Logic (Type Safe)
    ↓
JSON Serialization (model_dump())
    ↓
FastMCP (JSON)
```

## Best Practices

### 1. **Always Validate at Boundaries**
```python
# At API boundary
raw_data = await self.make_request(...)
return CategoryResponse.model_validate(raw_data)  # Validate here
```

### 2. **Use Type Hints Everywhere**
```python
async def process_categories(response: CategoryResponse) -> Dict[str, Any]:
    # Function signature is self-documenting
```

### 3. **Handle Validation Errors**
```python
try:
    response = CategoryResponse.model_validate(data)
except ValidationError as e:
    logger.error(f"Invalid API response: {e}")
    raise APIError("Invalid response from KiotViet API")
```

### 4. **Convert to JSON for FastMCP**
```python
# Tools must return JSON-serializable data
return typed_response.model_dump()  # Pydantic → Dict
```

## Result

**Perfect type safety** throughout the application while maintaining **FastMCP compatibility**:

- ✅ **Development**: Full type safety and IDE support
- ✅ **Runtime**: Automatic validation catches errors early  
- ✅ **Integration**: JSON serialization for FastMCP
- ✅ **Maintenance**: Self-documenting, easy to extend

This implementation provides the **best of both worlds**: the safety and developer experience of typed entities, with the flexibility and compatibility of JSON APIs.
