"""Tests for CategoryService."""

import pytest
from unittest.mock import AsyncMock
from src.albatross_kiotviet_mcp.application.services.category_service import (
    CategoryService,
    CategoryServiceError
)


class TestCategoryService:
    """Unit tests for CategoryService (with mocks)."""

    @pytest.fixture
    def category_service(self, mock_api_client):
        """Create category service instance with mocked API client."""
        return CategoryService(mock_api_client)

    @pytest.mark.asyncio
    async def test_get_categories_success(self, category_service, mock_api_client, sample_category_response):
        """Test successful category retrieval."""
        # Arrange
        mock_api_client.get_categories.return_value = sample_category_response

        # Act
        result = await category_service.get_categories(
            page_size=50,
            current_item=0,
            order_direction="Asc",
            hierarchical_data=False
        )

        # Assert
        assert result == sample_category_response
        mock_api_client.get_categories.assert_called_once_with(
            page_size=50,
            current_item=0,
            order_direction="Asc",
            hierarchical_data=False
        )

    @pytest.mark.asyncio
    async def test_get_categories_with_custom_params(self, category_service, mock_api_client, sample_category_response):
        """Test category retrieval with custom parameters."""
        # Arrange
        mock_api_client.get_categories.return_value = sample_category_response

        # Act
        result = await category_service.get_categories(
            page_size=25,
            current_item=10,
            order_direction="Desc",
            hierarchical_data=True
        )

        # Assert
        assert result == sample_category_response
        mock_api_client.get_categories.assert_called_once_with(
            page_size=25,
            current_item=10,
            order_direction="Desc",
            hierarchical_data=True
        )

    @pytest.mark.asyncio
    async def test_get_categories_invalid_page_size(self, category_service):
        """Test category retrieval with invalid page size."""
        # Act & Assert
        with pytest.raises(CategoryServiceError, match="Invalid parameters"):
            await category_service.get_categories(page_size=0)

        with pytest.raises(CategoryServiceError, match="Invalid parameters"):
            await category_service.get_categories(page_size=101)

    @pytest.mark.asyncio
    async def test_get_categories_invalid_current_item(self, category_service):
        """Test category retrieval with invalid current item."""
        # Act & Assert
        with pytest.raises(CategoryServiceError, match="Invalid parameters"):
            await category_service.get_categories(current_item=-1)

    @pytest.mark.asyncio
    async def test_get_categories_invalid_order_direction(self, category_service):
        """Test category retrieval with invalid order direction."""
        # Act & Assert
        with pytest.raises(CategoryServiceError, match="Invalid parameters"):
            await category_service.get_categories(order_direction="Invalid")

    @pytest.mark.asyncio
    async def test_get_categories_api_error(self, category_service, mock_api_client):
        """Test category retrieval when API client raises exception."""
        # Arrange
        mock_api_client.get_categories.side_effect = Exception("API Error")

        # Act & Assert
        with pytest.raises(CategoryServiceError, match="Category retrieval failed"):
            await category_service.get_categories()

    @pytest.mark.asyncio
    async def test_get_category_count_success(self, category_service, mock_api_client, sample_category_response):
        """Test successful category count retrieval."""
        # Arrange
        sample_category_response.total = 147
        mock_api_client.get_categories.return_value = sample_category_response

        # Act
        result = await category_service.get_category_count()

        # Assert
        assert result == 147
        mock_api_client.get_categories.assert_called_once_with(
            page_size=1,
            current_item=0,
            order_direction="Asc",
            hierarchical_data=False
        )

    @pytest.mark.asyncio
    async def test_get_category_count_error(self, category_service, mock_api_client):
        """Test category count retrieval when service raises exception."""
        # Arrange
        mock_api_client.get_categories.side_effect = Exception("API Error")

        # Act & Assert
        with pytest.raises(CategoryServiceError, match="Category count retrieval failed"):
            await category_service.get_category_count()

    @pytest.mark.asyncio
    async def test_validate_category_exists_true(self, category_service, mock_api_client, sample_category_response):
        """Test category existence validation when category exists."""
        # Arrange
        sample_category_response.data[0].categoryId = 123
        mock_api_client.get_categories.return_value = sample_category_response

        # Act
        result = await category_service.validate_category_exists(123)

        # Assert
        assert result is True

    @pytest.mark.asyncio
    async def test_validate_category_exists_false(self, category_service, mock_api_client, sample_category_response):
        """Test category existence validation when category doesn't exist."""
        # Arrange
        sample_category_response.data[0].categoryId = 123
        mock_api_client.get_categories.return_value = sample_category_response

        # Act
        result = await category_service.validate_category_exists(999)

        # Assert
        assert result is False

    @pytest.mark.asyncio
    async def test_validate_category_exists_error(self, category_service, mock_api_client):
        """Test category existence validation when service raises exception."""
        # Arrange
        mock_api_client.get_categories.side_effect = Exception("API Error")

        # Act & Assert
        with pytest.raises(CategoryServiceError, match="Category validation failed"):
            await category_service.validate_category_exists(123)
