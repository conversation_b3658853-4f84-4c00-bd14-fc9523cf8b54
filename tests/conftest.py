"""Shared test fixtures for KiotViet MCP tests."""

import pytest
from unittest.mock import AsyncMock, MagicMock
from src.albatross_kiotviet_mcp.domain.entities.category import Category, CategoryResponse
from src.albatross_kiotviet_mcp.infrastructure.config.settings import KiotVietConfig


@pytest.fixture
def mock_config():
    """Mock KiotViet configuration."""
    return KiotVietConfig(
        client_id="test_client_id",
        client_secret="test_client_secret",
        retailer="test_retailer",
        auth_url="https://test.auth.url",
        api_base_url="https://test.api.url",
        request_timeout=30,
        max_retries=3,
        retry_delay=1.0,
        token_buffer_seconds=300
    )


@pytest.fixture
def mock_api_client():
    """Mock KiotViet API client."""
    mock_client = AsyncMock()
    
    # Mock context manager behavior
    mock_client.__aenter__ = AsyncMock(return_value=mock_client)
    mock_client.__aexit__ = AsyncMock(return_value=None)
    
    return mock_client


@pytest.fixture
def sample_category():
    """Sample category entity for testing."""
    return Category(
        categoryId=1,
        categoryName="Test Category",
        retailerId=12345,
        modifiedDate="2024-01-01T00:00:00Z",
        createdDate="2024-01-01T00:00:00Z",
        rank=1
    )


@pytest.fixture
def sample_category_response(sample_category):
    """Sample category response for testing."""
    return CategoryResponse(
        total=1,
        pageSize=50,
        data=[sample_category]
    )


@pytest.fixture
def mock_category_service():
    """Mock category service."""
    mock_service = AsyncMock()
    return mock_service
