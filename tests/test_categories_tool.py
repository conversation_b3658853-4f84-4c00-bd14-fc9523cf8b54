"""Tests for CategoriesTool."""

import pytest
from unittest.mock import AsyncMock
from src.albatross_kiotviet_mcp.tools.categories.categories_tool import (
    CategoriesTool,
    CategoriesToolError
)
from src.albatross_kiotviet_mcp.application.services.category_service import CategoryServiceError


class TestCategoriesTool:
    """Unit tests for CategoriesTool (with mocks)."""

    @pytest.fixture
    def categories_tool(self, mock_category_service):
        """Create categories tool instance with mocked category service."""
        return CategoriesTool(mock_category_service)

    @pytest.mark.asyncio
    async def test_execute_success(self, categories_tool, mock_category_service, sample_category_response):
        """Test successful tool execution."""
        # Arrange
        mock_category_service.get_categories.return_value = sample_category_response
        expected_result = sample_category_response.model_dump()

        # Act
        result = await categories_tool.execute(
            page_size=50,
            current_item=0,
            order_direction="Asc",
            hierarchical_data=False
        )

        # Assert
        assert result == expected_result
        mock_category_service.get_categories.assert_called_once_with(
            page_size=50,
            current_item=0,
            order_direction="Asc",
            hierarchical_data=False
        )

    @pytest.mark.asyncio
    async def test_execute_with_custom_params(self, categories_tool, mock_category_service, sample_category_response):
        """Test tool execution with custom parameters."""
        # Arrange
        mock_category_service.get_categories.return_value = sample_category_response
        expected_result = sample_category_response.model_dump()

        # Act
        result = await categories_tool.execute(
            page_size=25,
            current_item=10,
            order_direction="Desc",
            hierarchical_data=True
        )

        # Assert
        assert result == expected_result
        mock_category_service.get_categories.assert_called_once_with(
            page_size=25,
            current_item=10,
            order_direction="Desc",
            hierarchical_data=True
        )

    @pytest.mark.asyncio
    async def test_execute_category_service_error(self, categories_tool, mock_category_service):
        """Test tool execution when category service raises CategoryServiceError."""
        # Arrange
        mock_category_service.get_categories.side_effect = CategoryServiceError("Service error")

        # Act & Assert
        with pytest.raises(CategoriesToolError, match="Category service error"):
            await categories_tool.execute()

    @pytest.mark.asyncio
    async def test_execute_unexpected_error(self, categories_tool, mock_category_service):
        """Test tool execution when category service raises unexpected error."""
        # Arrange
        mock_category_service.get_categories.side_effect = Exception("Unexpected error")

        # Act & Assert
        with pytest.raises(CategoriesToolError, match="Tool execution failed"):
            await categories_tool.execute()

    def test_tool_properties(self, categories_tool):
        """Test tool properties are set correctly."""
        assert categories_tool.name == "get_categories"
        assert "Get product categories from KiotViet API" in categories_tool.description

    def test_pre_execute_no_validation(self, categories_tool):
        """Test pre_execute method (currently no validation)."""
        # Should not raise any exceptions
        categories_tool.pre_execute(
            page_size=50,
            current_item=0,
            order_direction="Asc",
            hierarchical_data=False
        )

    def test_wrapper_function_creation(self, categories_tool):
        """Test wrapper function creation for FastMCP."""
        wrapper_func = categories_tool.get_wrapper_function()
        
        assert callable(wrapper_func)
        assert wrapper_func.__name__ == "get_categories"
        assert "Get product categories from KiotViet API" in wrapper_func.__doc__

    @pytest.mark.asyncio
    async def test_wrapper_function_execution(self, categories_tool, mock_category_service, sample_category_response):
        """Test wrapper function execution."""
        # Arrange
        mock_category_service.get_categories.return_value = sample_category_response
        expected_result = sample_category_response.model_dump()
        wrapper_func = categories_tool.get_wrapper_function()

        # Act
        result = await wrapper_func(
            page_size=25,
            current_item=5,
            order_direction="Desc",
            hierarchical_data=True
        )

        # Assert
        assert result == expected_result
        mock_category_service.get_categories.assert_called_once_with(
            page_size=25,
            current_item=5,
            order_direction="Desc",
            hierarchical_data=True
        )
