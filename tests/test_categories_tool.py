"""Tests for categories tool."""

import pytest
import os
from unittest.mock import AsyncMock
from src.albatross_kiotviet_mcp.tools.categories.categories_tool import CategoriesTool, CategoriesToolError
from src.albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
from src.albatross_kiotviet_mcp.infrastructure.config.settings import get_config


class TestCategoriesTool:
    """Test cases for CategoriesTool."""
    
    @pytest.fixture
    def categories_tool(self, mock_api_client):
        """Create categories tool instance for testing."""
        return CategoriesTool(mock_api_client)
    
    @pytest.mark.asyncio
    async def test_execute_with_defaults(self, categories_tool, mock_api_client, sample_categories_response):
        """Test categories tool execution with default parameters."""
        mock_api_client.get_categories.return_value = sample_categories_response
        
        result = await categories_tool.execute()
        
        assert result == sample_categories_response
        mock_api_client.get_categories.assert_called_once_with(
            page_size=50,
            current_item=0,
            order_direction='Asc',
            hierarchical_data=False
        )
    
    @pytest.mark.asyncio
    async def test_execute_with_custom_parameters(self, categories_tool, mock_api_client, sample_categories_response):
        """Test categories tool execution with custom parameters."""
        mock_api_client.get_categories.return_value = sample_categories_response
        
        result = await categories_tool.execute(
            page_size=25,
            current_item=10,
            order_direction='Desc',
            hierarchical_data=True
        )
        
        assert result == sample_categories_response
        mock_api_client.get_categories.assert_called_once_with(
            page_size=25,
            current_item=10,
            order_direction='Desc',
            hierarchical_data=True
        )
    
    def test_validate_parameters_valid(self, categories_tool):
        """Test parameter validation with valid parameters."""
        # Should not raise any exception
        categories_tool.validate_parameters(
            page_size=50,
            current_item=0,
            order_direction='Asc'
        )
    
    def test_validate_parameters_invalid_page_size(self, categories_tool):
        """Test parameter validation with invalid page_size."""
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            categories_tool.validate_parameters(page_size=0)
        
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            categories_tool.validate_parameters(page_size=101)
        
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            categories_tool.validate_parameters(page_size="invalid")
    
    def test_validate_parameters_invalid_current_item(self, categories_tool):
        """Test parameter validation with invalid current_item."""
        with pytest.raises(ValueError, match="current_item must be a non-negative integer"):
            categories_tool.validate_parameters(current_item=-1)
        
        with pytest.raises(ValueError, match="current_item must be a non-negative integer"):
            categories_tool.validate_parameters(current_item="invalid")
    
    def test_validate_parameters_invalid_order_direction(self, categories_tool):
        """Test parameter validation with invalid order_direction."""
        with pytest.raises(ValueError, match="order_direction must be 'Asc' or 'Desc'"):
            categories_tool.validate_parameters(order_direction='Invalid')
    
    @pytest.mark.asyncio
    async def test_execute_with_validation_error(self, categories_tool):
        """Test categories tool execution with validation error."""
        with pytest.raises(Exception, match="Tool 'get_categories' execution failed"):
            await categories_tool.execute(page_size=0)
    
    @pytest.mark.asyncio
    async def test_execute_with_api_error(self, categories_tool, mock_api_client):
        """Test categories tool execution with API error."""
        mock_api_client.get_categories.side_effect = Exception("API Error")

        with pytest.raises(Exception, match="Tool 'get_categories' execution failed"):
            await categories_tool.execute()
    
    def test_tool_properties(self, categories_tool):
        """Test tool properties."""
        assert categories_tool.name == "get_categories"
        assert "Get product categories from KiotViet API" in categories_tool.description


class TestCategoriesToolIntegration:
    """Integration tests for CategoriesTool with real API calls."""

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_api_call_categories(self):
        """Test real API call to get categories - NO MOCKS!"""
        print("\n🚀 Starting REAL API integration test...")

        # Step 1: Load real configuration
        print("📋 Step 1: Loading configuration...")
        try:
            config = get_config()
            print(f"✅ Config loaded for retailer: {config.retailer}")
            print(f"   API Base URL: {config.api_base_url}")
            print(f"   Client ID: {config.client_id[:8]}...")
        except Exception as e:
            print(f"❌ Config loading failed: {e}")
            pytest.skip(f"Configuration not available: {e}")

        # Step 2: Create real API client
        print("\n🔌 Step 2: Creating real API client...")
        api_client = KiotVietAPIClient(config)
        print("✅ API client created")

        # Step 3: Create tool with real client
        print("\n🛠️ Step 3: Creating categories tool...")
        categories_tool = CategoriesTool(api_client)
        print(f"✅ Tool created: {categories_tool.name}")

        # Step 4: Execute real API call
        print("\n📡 Step 4: Making REAL API call...")
        try:
            result = await categories_tool.execute(
                page_size=5,  # Small page size for testing
                current_item=0,
                order_direction="Asc",
                hierarchical_data=False
            )

            print("✅ API call successful!")
            print(f"📊 Result type: {type(result)}")
            print(f"📊 Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

            # Step 5: Validate response structure
            print("\n🔍 Step 5: Validating response...")
            assert isinstance(result, dict), f"Expected dict, got {type(result)}"
            assert "data" in result, "Response missing 'data' field"
            assert "total" in result, "Response missing 'total' field"
            assert "page_size" in result, "Response missing 'page_size' field"
            assert "current_item" in result, "Response missing 'current_item' field"

            print(f"✅ Response structure valid")
            print(f"📊 Total categories: {result.get('total', 'unknown')}")
            print(f"📊 Returned categories: {len(result.get('data', []))}")

            # Step 6: Validate category data
            if result.get('data'):
                first_category = result['data'][0]
                print(f"\n📋 First category sample:")
                print(f"   ID: {first_category.get('id')}")
                print(f"   Name: {first_category.get('name')}")
                print(f"   Description: {first_category.get('description', 'N/A')}")

                # Validate category structure
                assert 'id' in first_category, "Category missing 'id' field"
                assert 'name' in first_category, "Category missing 'name' field"
                print("✅ Category structure valid")

            print("\n🎉 Integration test PASSED! Real API working correctly.")
            return result

        except Exception as e:
            print(f"\n❌ API call failed: {e}")
            print(f"   Error type: {type(e)}")

            # Don't fail the test if it's just API credentials issue
            if "401" in str(e) or "authentication" in str(e).lower():
                pytest.skip(f"API authentication issue (expected in test env): {e}")
            else:
                raise  # Re-raise other errors

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_api_call_with_different_params(self):
        """Test real API call with different parameters."""
        print("\n🚀 Testing different parameters...")

        try:
            config = get_config()
            api_client = KiotVietAPIClient(config)
            categories_tool = CategoriesTool(api_client)

            # Test with hierarchical data
            result = await categories_tool.execute(
                page_size=3,
                current_item=0,
                order_direction="Desc",
                hierarchical_data=True
            )

            print(f"✅ Hierarchical test passed")
            print(f"📊 Categories with hierarchy: {len(result.get('data', []))}")

            assert isinstance(result, dict)
            assert "data" in result

        except Exception as e:
            if "401" in str(e) or "authentication" in str(e).lower():
                pytest.skip(f"API authentication issue: {e}")
            else:
                raise

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_tool_wrapper_function(self):
        """Test the wrapper function that FastMCP uses."""
        print("\n🚀 Testing wrapper function...")

        try:
            config = get_config()
            api_client = KiotVietAPIClient(config)
            categories_tool = CategoriesTool(api_client)

            # Get the wrapper function (what FastMCP actually calls)
            wrapper_func = categories_tool.get_wrapper_function()
            print(f"✅ Wrapper function: {wrapper_func.__name__}")

            # Call wrapper function directly
            result = await wrapper_func(
                page_size=2,
                current_item=0,
                order_direction="Asc",
                hierarchical_data=False
            )

            print(f"✅ Wrapper function call successful")
            print(f"📊 Result type: {type(result)}")

            assert isinstance(result, dict)
            assert "data" in result

        except Exception as e:
            if "401" in str(e) or "authentication" in str(e).lower():
                pytest.skip(f"API authentication issue: {e}")
            else:
                raise
