#!/usr/bin/env python3
"""
Debug script for categories tool - step by step debugging with breakpoints.
Perfect for debugging real API calls without pytest overhead.
"""

import asyncio
import sys
import traceback

# Add src to path
sys.path.insert(0, 'src')

async def debug_categories_tool():
    """Debug categories tool step by step - perfect for breakpoints!"""
    
    print("🐛 CATEGORIES TOOL DEBUG SESSION")
    print("=" * 60)
    print("💡 Set breakpoints anywhere in this function!")
    print("💡 Use your IDE debugger or add print() statements")
    print()
    
    # Step 1: Configuration
    print("📋 Step 1: Loading configuration...")
    try:
        from albatross_kiotviet_mcp.infrastructure.config.settings import get_config
        
        config = get_config()
        print(f"✅ Config loaded:")
        print(f"   Retailer: {config.retailer}")
        print(f"   API Base URL: {config.api_base_url}")
        print(f"   Client ID: {config.client_id[:8]}...")
        print(f"   Timeout: {config.request_timeout}s")
        print(f"   Max Retries: {config.max_retries}")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect config
        breakpoint_1 = config  # You can inspect this in debugger
        
    except Exception as e:
        print(f"❌ Config failed: {e}")
        traceback.print_exc()
        return
    
    # Step 2: API Client
    print(f"\n🔌 Step 2: Creating API client...")
    try:
        from albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
        
        api_client = KiotVietAPIClient(config)
        print(f"✅ API client created: {type(api_client)}")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect api_client
        breakpoint_2 = api_client
        
    except Exception as e:
        print(f"❌ API client creation failed: {e}")
        traceback.print_exc()
        return
    
    # Step 3: Categories Tool
    print(f"\n🛠️ Step 3: Creating categories tool...")
    try:
        from albatross_kiotviet_mcp.tools.categories.categories_tool import CategoriesTool
        
        categories_tool = CategoriesTool(api_client)
        print(f"✅ Tool created:")
        print(f"   Name: {categories_tool.name}")
        print(f"   Description: {categories_tool.description[:50]}...")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect tool
        breakpoint_3 = categories_tool
        
    except Exception as e:
        print(f"❌ Tool creation failed: {e}")
        traceback.print_exc()
        return
    
    # Step 4: Test Parameters
    print(f"\n⚙️ Step 4: Setting up test parameters...")
    test_params = {
        "page_size": 3,  # Small for debugging
        "current_item": 0,
        "order_direction": "Asc",
        "hierarchical_data": False
    }
    
    print(f"✅ Test parameters:")
    for key, value in test_params.items():
        print(f"   {key}: {value}")
    
    # 🐛 BREAKPOINT: Set breakpoint here to modify parameters
    breakpoint_4 = test_params
    
    # Step 5: Parameter Validation
    print(f"\n🔍 Step 5: Validating parameters...")
    try:
        categories_tool.validate_parameters(**test_params)
        print(f"✅ Parameters validation passed")
        
    except Exception as e:
        print(f"❌ Parameter validation failed: {e}")
        traceback.print_exc()
        return
    
    # Step 6: Real API Call
    print(f"\n📡 Step 6: Making REAL API call...")
    try:
        print(f"   Calling: categories_tool.execute(**{test_params})")
        
        # 🐛 BREAKPOINT: Set breakpoint here before API call
        breakpoint_5 = "About to make API call"
        
        result = await categories_tool.execute(**test_params)
        
        print(f"🎉 API call successful!")
        print(f"   Result type: {type(result)}")
        print(f"   Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect result
        breakpoint_6 = result
        
    except Exception as e:
        print(f"❌ API call failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        traceback.print_exc()
        
        # 🐛 BREAKPOINT: Set breakpoint here to debug errors
        breakpoint_error = e
        return
    
    # Step 7: Response Analysis
    print(f"\n🔍 Step 7: Analyzing response...")
    try:
        if isinstance(result, dict):
            print(f"📊 Response analysis:")
            print(f"   Total categories: {result.get('total', 'N/A')}")
            print(f"   Page size: {result.get('pageSize', result.get('page_size', 'N/A'))}")
            print(f"   Data count: {len(result.get('data', []))}")
            
            # Analyze first category
            if result.get('data'):
                first_cat = result['data'][0]
                print(f"\n📋 First category:")
                for key, value in first_cat.items():
                    print(f"   {key}: {value}")
                
                # 🐛 BREAKPOINT: Set breakpoint here to inspect category data
                breakpoint_7 = first_cat
        
        print(f"\n✅ Response analysis complete")
        
    except Exception as e:
        print(f"❌ Response analysis failed: {e}")
        traceback.print_exc()
    
    # Step 8: Wrapper Function Test
    print(f"\n🔧 Step 8: Testing wrapper function...")
    try:
        wrapper_func = categories_tool.get_wrapper_function()
        print(f"✅ Wrapper function obtained: {wrapper_func.__name__}")
        
        # Test wrapper function call
        wrapper_result = await wrapper_func(
            page_size=2,
            current_item=0,
            order_direction="Desc",
            hierarchical_data=True
        )
        
        print(f"✅ Wrapper function call successful!")
        print(f"   Wrapper result type: {type(wrapper_result)}")
        print(f"   Wrapper result keys: {list(wrapper_result.keys()) if isinstance(wrapper_result, dict) else 'Not dict'}")
        
        # 🐛 BREAKPOINT: Set breakpoint here to compare results
        breakpoint_8 = {
            "direct_result": result,
            "wrapper_result": wrapper_result
        }
        
    except Exception as e:
        print(f"❌ Wrapper function test failed: {e}")
        traceback.print_exc()
    
    print(f"\n🎉 DEBUG SESSION COMPLETED!")
    print(f"=" * 60)
    print(f"💡 All breakpoints available for inspection")
    print(f"💡 Set breakpoints at any 'breakpoint_X = ...' line")
    
    return result

async def debug_with_custom_params():
    """Debug with custom parameters - modify as needed!"""
    
    print("\n🎯 CUSTOM PARAMETERS DEBUG")
    print("=" * 40)
    
    # 🔧 MODIFY THESE PARAMETERS FOR TESTING:
    custom_params = {
        "page_size": 10,        # Try different sizes
        "current_item": 0,      # Try pagination
        "order_direction": "Desc",  # Try different order
        "hierarchical_data": True   # Try hierarchical
    }
    
    print(f"🧪 Testing with custom parameters: {custom_params}")
    
    try:
        from albatross_kiotviet_mcp.infrastructure.config.settings import get_config
        from albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
        from albatross_kiotviet_mcp.tools.categories.categories_tool import CategoriesTool
        
        config = get_config()
        api_client = KiotVietAPIClient(config)
        categories_tool = CategoriesTool(api_client)
        
        result = await categories_tool.execute(**custom_params)
        
        print(f"✅ Custom test successful!")
        print(f"   Categories returned: {len(result.get('data', []))}")
        print(f"   Total available: {result.get('total')}")
        
        return result
        
    except Exception as e:
        print(f"❌ Custom test failed: {e}")
        traceback.print_exc()
        return None

def main():
    """Main debug function - run this for debugging!"""
    
    print("🚀 CATEGORIES TOOL DEBUGGER")
    print("Choose your debugging mode:")
    print("1. Full step-by-step debug")
    print("2. Custom parameters test")
    print("3. Both")
    
    choice = input("\nEnter choice (1/2/3) or press Enter for full debug: ").strip()
    
    if choice == "2":
        print("\n" + "="*60)
        result = asyncio.run(debug_with_custom_params())
    elif choice == "3":
        print("\n" + "="*60)
        result1 = asyncio.run(debug_categories_tool())
        print("\n" + "="*60)
        result2 = asyncio.run(debug_with_custom_params())
    else:
        print("\n" + "="*60)
        result = asyncio.run(debug_categories_tool())
    
    print(f"\n🎯 DEBUG COMPLETE!")
    print(f"💡 Set breakpoints in the functions above for detailed debugging")

if __name__ == "__main__":
    main()
