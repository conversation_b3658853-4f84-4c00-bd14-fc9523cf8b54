"""Integration tests for Categories functionality."""

import pytest
from src.albatross_kiotviet_mcp.infrastructure.config.settings import get_config
from src.albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
from src.albatross_kiotviet_mcp.application.services.category_service import CategoryService
from src.albatross_kiotviet_mcp.tools.categories.categories_tool import CategoriesTool


class TestCategoriesIntegration:
    """Integration tests for Categories (real API calls)."""

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_category_service_real_api(self):
        """Test CategoryService with real KiotViet API."""
        try:
            # Arrange
            config = get_config()
            api_client = KiotVietAPIClient(config)
            category_service = CategoryService(api_client)

            # Act
            result = await category_service.get_categories(
                page_size=5,
                current_item=0,
                order_direction="Asc",
                hierarchical_data=False
            )

            # Assert
            assert result.total >= 0
            assert result.pageSize == 5
            assert len(result.data) <= 5
            assert all(hasattr(cat, 'categoryId') for cat in result.data)
            assert all(hasattr(cat, 'categoryName') for cat in result.data)

        except Exception as e:
            if "401" in str(e) or "authentication" in str(e).lower():
                pytest.skip(f"API authentication issue: {e}")
            else:
                raise

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_categories_tool_real_api(self):
        """Test CategoriesTool with real KiotViet API."""
        try:
            # Arrange
            config = get_config()
            api_client = KiotVietAPIClient(config)
            category_service = CategoryService(api_client)
            categories_tool = CategoriesTool(category_service)

            # Act
            result = await categories_tool.execute(
                page_size=3,
                current_item=0,
                order_direction="Asc",
                hierarchical_data=False
            )

            # Assert
            assert isinstance(result, dict)
            assert "total" in result
            assert "pageSize" in result
            assert "data" in result
            assert isinstance(result["data"], list)
            assert len(result["data"]) <= 3

            if result["data"]:
                first_category = result["data"][0]
                assert "categoryId" in first_category
                assert "categoryName" in first_category

        except Exception as e:
            if "401" in str(e) or "authentication" in str(e).lower():
                pytest.skip(f"API authentication issue: {e}")
            else:
                raise

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_categories_tool_wrapper_function(self):
        """Test CategoriesTool wrapper function with real API."""
        try:
            # Arrange
            config = get_config()
            api_client = KiotVietAPIClient(config)
            category_service = CategoryService(api_client)
            categories_tool = CategoriesTool(category_service)
            wrapper_func = categories_tool.get_wrapper_function()

            # Act
            result = await wrapper_func(
                page_size=2,
                current_item=0,
                order_direction="Desc",
                hierarchical_data=True
            )

            # Assert
            assert isinstance(result, dict)
            assert "total" in result
            assert "data" in result
            assert len(result["data"]) <= 2

        except Exception as e:
            if "401" in str(e) or "authentication" in str(e).lower():
                pytest.skip(f"API authentication issue: {e}")
            else:
                raise

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_category_count_real_api(self):
        """Test category count with real API."""
        try:
            # Arrange
            config = get_config()
            api_client = KiotVietAPIClient(config)
            category_service = CategoryService(api_client)

            # Act
            count = await category_service.get_category_count()

            # Assert
            assert isinstance(count, int)
            assert count >= 0

        except Exception as e:
            if "401" in str(e) or "authentication" in str(e).lower():
                pytest.skip(f"API authentication issue: {e}")
            else:
                raise

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_validate_category_exists_real_api(self):
        """Test category existence validation with real API."""
        try:
            # Arrange
            config = get_config()
            api_client = KiotVietAPIClient(config)
            category_service = CategoryService(api_client)

            # Get a real category ID first
            categories = await category_service.get_categories(page_size=1)
            if not categories.data:
                pytest.skip("No categories available for testing")

            real_category_id = categories.data[0].categoryId

            # Act & Assert - Test existing category
            exists = await category_service.validate_category_exists(real_category_id)
            assert exists is True

            # Act & Assert - Test non-existing category
            fake_category_id = 999999999  # Very unlikely to exist
            exists = await category_service.validate_category_exists(fake_category_id)
            assert exists is False

        except Exception as e:
            if "401" in str(e) or "authentication" in str(e).lower():
                pytest.skip(f"API authentication issue: {e}")
            else:
                raise

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_end_to_end_architecture_flow(self):
        """Test complete architecture flow: Tool → Service → Infrastructure → API."""
        try:
            # Arrange - Build the complete dependency chain
            config = get_config()
            api_client = KiotVietAPIClient(config)
            category_service = CategoryService(api_client)
            categories_tool = CategoriesTool(category_service)

            # Act - Execute through the tool (highest level)
            result = await categories_tool.execute(page_size=1)

            # Assert - Verify the complete flow worked
            assert isinstance(result, dict)
            assert "total" in result
            assert "data" in result

            # Verify data structure matches domain entities
            if result["data"]:
                category = result["data"][0]
                assert "categoryId" in category
                assert "categoryName" in category
                assert isinstance(category["categoryId"], int)
                assert isinstance(category["categoryName"], str)

        except Exception as e:
            if "401" in str(e) or "authentication" in str(e).lower():
                pytest.skip(f"API authentication issue: {e}")
            else:
                raise
