"""Branches MCP tool implementation."""

from typing import Dict, Any, Callable
import logging
from ..base.base_tool import Base<PERSON>PTool
from ...domain.interfaces.api_client import IKiotVietAPIClient

logger = logging.getLogger(__name__)


class BranchesToolError(Exception):
    """Custom exception for branches tool errors."""
    pass


class BranchesTool(BaseMCPTool):
    """MCP tool for retrieving branches from KiotViet."""
    
    def __init__(self, api_client: IKiotVietAPIClient):
        super().__init__(
            name="get_branches",
            description="Get branches from KiotViet API with pagination support"
        )
        self.api_client = api_client
    
    def validate_parameters(self, **kwargs) -> None:
        """Validate branches tool parameters.
        """
        page_size = kwargs.get('page_size', 50)
        current_item = kwargs.get('current_item', 0)
        order_direction = kwargs.get('order_direction', 'Asc')
        
        if not isinstance(page_size, int) or page_size < 1 or page_size > 100:
            raise ValueError("page_size must be an integer between 1 and 100")
        
        if not isinstance(current_item, int) or current_item < 0:
            raise ValueError("current_item must be a non-negative integer")
        
        if order_direction not in ['Asc', 'Desc']:
            raise ValueError("order_direction must be 'Asc' or 'Desc'")
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the branches tool.
        
        Args:
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
            
        Returns:
            Dictionary containing branches data and pagination info
        """
        try:
            # Set defaults
            page_size = kwargs.get('page_size', 50)
            current_item = kwargs.get('current_item', 0)
            order_direction = kwargs.get('order_direction', 'Asc')
            
            # Validate parameters
            self.validate_parameters(**kwargs)
            
            self.logger.info(
                f"Fetching branches: page_size={page_size}, "
                f"current_item={current_item}, order_direction={order_direction}"
            )
            
            # Make API call
            async with self.api_client:
                result = await self.api_client.get_branches(
                    page_size=page_size,
                    current_item=current_item,
                    order_direction=order_direction
                )
            
            self.logger.info(f"Successfully retrieved branches: {len(result.get('data', []))} items")
            return result
            
        except ValueError as e:
            await self.handle_error(e, {"parameters": kwargs})
            raise BranchesToolError(f"Invalid parameters: {str(e)}")
        except Exception as e:
            await self.handle_error(e, {"parameters": kwargs})
            raise BranchesToolError(f"Failed to retrieve branches: {str(e)}")

    def _create_wrapper_function(self) -> Callable:
        """Create wrapper function with proper signature for FastMCP registration."""
        async def get_branches(
            page_size: int = 50,
            current_item: int = 0,
            order_direction: str = "Asc"
        ) -> Dict[str, Any]:
            """Get branches from KiotViet API.

            Args:
                page_size: Number of items per page (default: 50, max: 100)
                current_item: Starting item index for pagination (default: 0)
                order_direction: Sort order - "Asc" or "Desc" (default: "Asc")

            Returns:
                Dictionary containing branches data and pagination info
            """
            return await self.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction
            )

        return get_branches
