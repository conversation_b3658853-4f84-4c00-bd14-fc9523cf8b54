"""Categories MCP tool implementation."""

from typing import Dict, Any, Callable
import logging
from ..base.base_tool import Base<PERSON>PTool
from ...application.services.category_service import CategoryService, CategoryServiceError

logger = logging.getLogger(__name__)


class CategoriesToolError(Exception):
    """Custom exception for categories tool errors."""
    pass


class CategoriesTool(BaseMCPTool):
    """MCP tool for retrieving product categories from KiotViet."""

    def __init__(self, category_service: CategoryService):
        super().__init__(
            name="get_categories",
            description="Get product categories from KiotViet API with pagination support"
        )
        self.category_service = category_service
    
    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Execute the categories tool.

        Args:
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
            hierarchical_data: Whether to return hierarchical structure (default: False)

        Returns:
            Dictionary containing categories data and pagination info
        """
        try:
            # MCP tool-specific validation (if any)
            self.pre_execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )

            self.logger.info(f"Executing {self.name} tool")

            # Delegate to application service
            typed_result = await self.category_service.get_categories(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )

            self.logger.info(f"Tool {self.name} completed successfully")

            # Convert Pydantic model to dict for FastMCP JSON serialization
            return typed_result.model_dump()

        except CategoryServiceError as e:
            self.logger.error(f"Category service error: {e}")
            raise CategoriesToolError(f"Category service error: {str(e)}")
        except Exception as e:
            self.logger.error(f"Unexpected error in {self.name}: {e}")
            raise CategoriesToolError(f"Tool execution failed: {str(e)}")

    def pre_execute(self, **kwargs) -> None:  # noqa: ARG002
        """Validate MCP tool-specific parameters.

        Business validation is handled by CategoryService.
        This method is reserved for MCP-specific validation (permissions, rate limiting, etc.).

        Args:
            **kwargs: Parameters to validate

        Raises:
            ValueError: If MCP tool-specific parameters are invalid
        """
        # Currently no MCP-specific validation needed
        # Future extensions: user permissions, rate limiting, tool constraints
        pass


    def _create_wrapper_function(self) -> Callable:
        """Create wrapper function with proper signature for FastMCP registration."""
        async def get_categories(
            page_size: int = 50,
            current_item: int = 0,
            order_direction: str = "Asc",
            hierarchical_data: bool = False
        ) -> Dict[str, Any]:
            """Get product categories from KiotViet API.

            Args:
                page_size: Number of items per page (default: 50, max: 100)
                current_item: Starting item index for pagination (default: 0)
                order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
                hierarchical_data: Whether to return hierarchical structure (default: False)

            Returns:
                Dictionary containing categories data and pagination info
            """
            return await self.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )

        return get_categories

