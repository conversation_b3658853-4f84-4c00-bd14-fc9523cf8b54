"""Products MCP tool implementation."""

from typing import Dict, Any, Optional
import logging
from ..base.base_tool import BaseMCPTool
from ...domain.interfaces.api_client import IKiotVietAPIClient

logger = logging.getLogger(__name__)


class ProductsToolError(Exception):
    """Custom exception for products tool errors."""
    pass


class ProductsTool(BaseMCPTool):
    """MCP tool for retrieving products from KiotViet."""
    
    def __init__(self, api_client: IKiotVietAPIClient):
        super().__init__(
            name="get_products",
            description="Get products from KiotViet API with pagination and filtering support"
        )
        self.api_client = api_client
    
    def validate_parameters(self, **kwargs) -> None:
        """Validate products tool parameters.
        
        Args:
            **kwargs: Parameters to validate
            
        Raises:
            ValueError: If parameters are invalid
        """
        page_size = kwargs.get('page_size', 50)
        current_item = kwargs.get('current_item', 0)
        order_direction = kwargs.get('order_direction', 'Asc')
        category_id = kwargs.get('category_id')
        
        # Validate page_size
        if not isinstance(page_size, int) or page_size < 1 or page_size > 100:
            raise ValueError("page_size must be an integer between 1 and 100")
        
        # Validate current_item
        if not isinstance(current_item, int) or current_item < 0:
            raise ValueError("current_item must be a non-negative integer")
        
        # Validate order_direction
        if order_direction not in ['Asc', 'Desc']:
            raise ValueError("order_direction must be 'Asc' or 'Desc'")
        
        # Validate category_id if provided
        if category_id is not None and (not isinstance(category_id, int) or category_id < 1):
            raise ValueError("category_id must be a positive integer")
    
    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        category_id: Optional[int] = None,
        include_inventory: bool = False
    ) -> Dict[str, Any]:
        """Execute the products tool.

        Args:
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
            category_id: Filter by category ID (optional)
            include_inventory: Include inventory information (default: False)

        Returns:
            Dictionary containing products data and pagination info
        """
        try:
            
            # Validate parameters
            self.validate_parameters(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                category_id=category_id,
                include_inventory=include_inventory
            )
            
            self.logger.info(
                f"Fetching products: page_size={page_size}, "
                f"current_item={current_item}, order_direction={order_direction}, "
                f"category_id={category_id}, include_inventory={include_inventory}"
            )
            
            # Make API call
            async with self.api_client:
                result = await self.api_client.get_products(
                    page_size=page_size,
                    current_item=current_item,
                    order_direction=order_direction,
                    category_id=category_id,
                    include_inventory=include_inventory
                )
            
            self.logger.info(f"Successfully retrieved products: {len(result.get('data', []))} items")
            return result
            
        except ValueError as e:
            await self.handle_error(e, {
                "page_size": page_size,
                "current_item": current_item,
                "order_direction": order_direction,
                "category_id": category_id,
                "include_inventory": include_inventory
            })
            raise ProductsToolError(f"Invalid parameters: {str(e)}")
        except Exception as e:
            await self.handle_error(e, {
                "page_size": page_size,
                "current_item": current_item,
                "order_direction": order_direction,
                "category_id": category_id,
                "include_inventory": include_inventory
            })
            raise ProductsToolError(f"Failed to retrieve products: {str(e)}")

