"""Category business service."""

import logging
from ...domain.interfaces.api_client import IKiotVietAPIClient
from ...domain.entities.category import CategoryResponse


logger = logging.getLogger(__name__)


class CategoryServiceError(Exception):
    """Custom exception for category service errors."""
    pass


class CategoryService:
    """Business service for category operations.
    
    This service encapsulates business logic for category operations,
    orchestrates API calls, and handles business validation.
    """

    def __init__(self, api_client: IKiotVietAPIClient):
        """Initialize category service.
        
        Args:
            api_client: KiotViet API client interface
        """
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)

    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> CategoryResponse:
        """Get categories with business logic and validation.

        Args:
            page_size: Number of items per page (1-100)
            current_item: Starting item index for pagination
            order_direction: Sort order - "Asc" or "Desc"
            hierarchical_data: Whether to return hierarchical structure

        Returns:
            CategoryResponse with validated data

        Raises:
            CategoryServiceError: If business validation fails or API call fails
        """
        try:
            # Business validation
            self._validate_get_categories_params(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )

            # Log business operation
            self.logger.info(
                f"Retrieving categories: page_size={page_size}, "
                f"current_item={current_item}, order_direction={order_direction}, "
                f"hierarchical_data={hierarchical_data}"
            )

            # Orchestrate API call
            async with self.api_client:
                result = await self.api_client.get_categories(
                    page_size=page_size,
                    current_item=current_item,
                    order_direction=order_direction,
                    hierarchical_data=hierarchical_data
                )

            # Business post-processing (if needed in future)
            processed_result = self._post_process_categories(result)

            self.logger.info(
                f"Successfully retrieved {len(processed_result.data)} categories "
                f"out of {processed_result.total} total"
            )

            return processed_result

        except ValueError as e:
            self.logger.error(f"Business validation failed: {e}")
            raise CategoryServiceError(f"Invalid parameters: {str(e)}")
        except Exception as e:
            self.logger.error(f"Failed to retrieve categories: {e}")
            raise CategoryServiceError(f"Category retrieval failed: {str(e)}")

    def _validate_get_categories_params(
        self,
        page_size: int,
        current_item: int,
        order_direction: str,
        hierarchical_data: bool
    ) -> None:
        """Validate business rules for get_categories parameters.
        
        Args:
            page_size: Number of items per page
            current_item: Starting item index
            order_direction: Sort order
            hierarchical_data: Hierarchical flag
            
        Raises:
            ValueError: If parameters violate business rules
        """
        # Business rule: Page size limits
        if not isinstance(page_size, int) or page_size < 1 or page_size > 100:
            raise ValueError("page_size must be an integer between 1 and 100")
        
        # Business rule: Pagination validation
        if not isinstance(current_item, int) or current_item < 0:
            raise ValueError("current_item must be a non-negative integer")
        
        # Business rule: Sort direction validation
        if order_direction not in ['Asc', 'Desc']:
            raise ValueError("order_direction must be 'Asc' or 'Desc'")
        
        # Business rule: Hierarchical data validation
        if not isinstance(hierarchical_data, bool):
            raise ValueError("hierarchical_data must be a boolean")

        # Business rule: Performance optimization
        if page_size > 50 and hierarchical_data:
            self.logger.warning(
                f"Large page size ({page_size}) with hierarchical data may impact performance"
            )

    def _post_process_categories(self, result: CategoryResponse) -> CategoryResponse:
        """Apply business post-processing to category results.
        
        This method can be extended in the future for:
        - Filtering based on business rules
        - Data enrichment
        - Business-specific transformations
        
        Args:
            result: Raw category response from API
            
        Returns:
            Processed category response
        """
        # Currently no post-processing needed
        # Future business logic can be added here:
        # - Filter inactive categories
        # - Add computed business fields
        # - Apply user-specific business rules
        
        return result

    async def get_category_count(self) -> int:
        """Get total number of categories (business convenience method).
        
        Returns:
            Total number of categories
            
        Raises:
            CategoryServiceError: If count retrieval fails
        """
        try:
            # Get first page to get total count
            result = await self.get_categories(page_size=1, current_item=0)
            return result.total
        except Exception as e:
            self.logger.error(f"Failed to get category count: {e}")
            raise CategoryServiceError(f"Category count retrieval failed: {str(e)}")

    async def validate_category_exists(self, category_id: int) -> bool:
        """Validate if a category exists (business validation method).
        
        This is a business method that could be used by other services
        or tools that need to validate category existence.
        
        Args:
            category_id: Category ID to validate
            
        Returns:
            True if category exists, False otherwise
            
        Raises:
            CategoryServiceError: If validation fails due to API error
        """
        try:
            # Get all categories and check if the ID exists
            # Note: This is a simple implementation. In a real system,
            # you might have a dedicated API endpoint for this.
            result = await self.get_categories(page_size=100)
            
            category_ids = [category.categoryId for category in result.data]
            exists = category_id in category_ids
            
            self.logger.debug(f"Category {category_id} exists: {exists}")
            return exists
            
        except Exception as e:
            self.logger.error(f"Failed to validate category existence: {e}")
            raise CategoryServiceError(f"Category validation failed: {str(e)}")
