"""Product domain entity."""

from typing import Optional, List
from pydantic import BaseModel, Field


class Product(BaseModel):
    """KiotViet product entity."""
    
    id: int = Field(description="Product ID")
    name: str = Field(description="Product name")
    code: Optional[str] = Field(default=None, description="Product code/SKU")
    description: Optional[str] = Field(default=None, description="Product description")
    category_id: Optional[int] = Field(default=None, description="Category ID")
    category_name: Optional[str] = Field(default=None, description="Category name")
    base_price: Optional[float] = Field(default=None, description="Base price")
    is_active: bool = Field(default=True, description="Whether product is active")
    has_variants: bool = Field(default=False, description="Whether product has variants")
    inventory_count: Optional[int] = Field(default=None, description="Inventory count")
    created_date: Optional[str] = Field(default=None, description="Creation date")
    modified_date: Optional[str] = Field(default=None, description="Last modification date")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class ProductResponse(BaseModel):
    """Response model for product API calls."""
    
    data: List[Product] = Field(description="List of products")
    total: int = Field(description="Total number of products")
    page_size: int = Field(description="Number of items per page")
    current_item: int = Field(description="Current item index")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
