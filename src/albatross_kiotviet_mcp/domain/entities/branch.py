"""Branch domain entity."""

from typing import Optional, List
from pydantic import BaseModel, Field


class Branch(BaseModel):
    """KiotViet branch entity."""
    
    id: int = Field(description="Branch ID")
    name: str = Field(description="Branch name")
    code: Optional[str] = Field(default=None, description="Branch code")
    address: Optional[str] = Field(default=None, description="Branch address")
    phone: Optional[str] = Field(default=None, description="Branch phone number")
    email: Optional[str] = Field(default=None, description="Branch email")
    is_active: bool = Field(default=True, description="Whether branch is active")
    is_default: bool = Field(default=False, description="Whether this is the default branch")
    created_date: Optional[str] = Field(default=None, description="Creation date")
    modified_date: Optional[str] = Field(default=None, description="Last modification date")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class BranchResponse(BaseModel):
    """Response model for branch API calls."""
    
    data: List[Branch] = Field(description="List of branches")
    total: int = Field(description="Total number of branches")
    page_size: int = Field(description="Number of items per page")
    current_item: int = Field(description="Current item index")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
