"""Category domain entity."""

from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict


class Category(BaseModel):
    """KiotViet product category entity."""
    
    id: int = Field(description="Category ID")
    name: str = Field(description="Category name")
    description: Optional[str] = Field(default=None, description="Category description")
    parent_id: Optional[int] = Field(default=None, description="Parent category ID")
    is_active: bool = Field(default=True, description="Whether category is active")
    created_date: Optional[str] = Field(default=None, description="Creation date")
    modified_date: Optional[str] = Field(default=None, description="Last modification date")
    
    model_config = ConfigDict(from_attributes=True)


class CategoryResponse(BaseModel):
    """Response model for category API calls."""
    
    data: List[Category] = Field(description="List of categories")
    total: int = Field(description="Total number of categories")
    page_size: int = Field(description="Number of items per page")
    current_item: int = Field(description="Current item index")
    
    model_config = ConfigDict(from_attributes=True)
