"""Category domain entity."""

from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict


class Category(BaseModel):
    """KiotViet product category entity."""
    
    categoryId: int = Field(description="Category ID")
    categoryName: str = Field(description="Category name")
    retailerId: Optional[int] = Field(default=None, description="Retailer ID")
    modifiedDate: Optional[str] = Field(default=None, description="modified date")
    createdDate: Optional[str] = Field(default=None, description="Created date")
    rank: Optional[int] = Field(default=None, description="Rank")
    
    model_config = ConfigDict(from_attributes=True)


class CategoryResponse(BaseModel):
    """Response model for category API calls."""
    
    total: int = Field(description="Total number of categories")
    pageSize: int = Field(description="Number of items per page")
    data: List[Category] = Field(description="List of categories")

    model_config = ConfigDict(from_attributes=True)
