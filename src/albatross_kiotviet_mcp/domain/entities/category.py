"""Category domain entity."""

from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict


class Category(BaseModel):
    """KiotViet product category entity."""
    
    categoryId: int = Field(description="Category ID")
    categoryName: str = Field(description="Category name")
    retailerId: Optional[int] = Field(default=None, description="Retailer ID")
    modifiedDate: Optional[str] = Field(default=None, description="modified date")
    createdDate: Optional[str] = Field(default=None, description="Created date")
    rank: Optional[int] = Field(default=None, description="Rank")

    # Computed properties for backward compatibility
    @property
    def id(self) -> int:
        """Alias for categoryId."""
        return self.categoryId

    @property
    def name(self) -> str:
        """Alias for categoryName."""
        return self.categoryName

    model_config = ConfigDict(from_attributes=True)


class CategoryResponse(BaseModel):
    """Response model for category API calls."""
    
    total: int = Field(description="Total number of categories")
    pageSize: int = Field(description="Number of items per page")
    data: List[Category] = Field(description="List of categories")

    # Computed properties for backward compatibility
    @property
    def page_size(self) -> int:
        """Alias for pageSize."""
        return self.pageSize

    @property
    def current_item(self) -> int:
        """Current item index (computed, KiotViet API doesn't return this)."""
        return 0  # Default to 0 since KiotViet doesn't provide this

    model_config = ConfigDict(from_attributes=True)
