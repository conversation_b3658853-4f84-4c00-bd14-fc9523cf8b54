"""KiotViet MCP Server."""

import logging
from typing import Op<PERSON>
from fastmcp import FastMCP

from .infrastructure.config.settings import get_config
from .infrastructure.api.kiotviet_client import KiotVietAPIClient
from .tools.categories.categories_tool import CategoriesTool
from .tools.products.products_tool import ProductsTool
from .tools.branches.branches_tool import BranchesTool

logger = logging.getLogger(__name__)

# Initialize FastMCP server
server = FastMCP(name="KiotVietMCPServer")

# Global instances (lazy-loaded)
_kiotviet_client: Optional[KiotVietAPIClient] = None
_config = None
_categories_tool: Optional[CategoriesTool] = None
_products_tool: Optional[ProductsTool] = None
_branches_tool: Optional[BranchesTool] = None


def get_kiotviet_client() -> KiotVietAPIClient:
    """Get or create KiotViet API client instance."""
    global _kiotviet_client, _config

    if _kiotviet_client is None:
        _config = get_config()
        _kiotviet_client = KiotVietAPIClient(_config)

    return _kiotviet_client


def get_categories_tool() -> CategoriesTool:
    """Get or create categories tool instance."""
    global _categories_tool

    if _categories_tool is None:
        client = get_kiotviet_client()
        _categories_tool = CategoriesTool(client)

    return _categories_tool


def get_products_tool() -> ProductsTool:
    """Get or create products tool instance."""
    global _products_tool

    if _products_tool is None:
        client = get_kiotviet_client()
        _products_tool = ProductsTool(client)

    return _products_tool


def get_branches_tool() -> BranchesTool:
    """Get or create branches tool instance."""
    global _branches_tool

    if _branches_tool is None:
        client = get_kiotviet_client()
        _branches_tool = BranchesTool(client)

    return _branches_tool


# MCP Tool Registration

def register_tools():
    """Register all MCP tools with the FastMCP server."""
    logger.info("Registering MCP tools...")

    # Get tool instances
    categories_tool = get_categories_tool()
    products_tool = get_products_tool()
    branches_tool = get_branches_tool()

    # Register tools using their wrapper functions
    server.tool(
        name=categories_tool.name,
        description=categories_tool.description
    )(categories_tool.get_wrapper_function())

    server.tool(
        name=products_tool.name,
        description=products_tool.description
    )(products_tool.get_wrapper_function())

    server.tool(
        name=branches_tool.name,
        description=branches_tool.description
    )(branches_tool.get_wrapper_function())

    logger.info("Successfully registered all MCP tools")


def run():
    """Entry point for running the FastMCP server."""
    logger.info(f"Starting {server.name}...")

    # Validate configuration on startup
    try:
        config = get_config()
        logger.info(f"Configuration loaded successfully for retailer: {config.retailer}")
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        raise

    # Register all tools
    register_tools()

    return server.run()