"""Configuration management for KiotViet MCP Server."""

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class KiotVietConfig(BaseSettings):
    """Configuration for KiotViet API integration."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8"
    )
    
    # KiotViet API credentials
    client_id: str = Field(alias="KIOTVIET_CLIENT_ID")
    client_secret: str = Field(alias="KIOTVIET_CLIENT_SECRET")
    retailer: str = Field(alias="KIOTVIET_RETAILER")
    
    # API endpoints
    auth_url: str = Field(
        default="https://id.kiotviet.vn/connect/token",
        alias="KIOTVIET_AUTH_URL"
    )
    api_base_url: str = Field(
        default="https://public.kiotapi.com",
        alias="KIOTVIET_API_BASE_URL"
    )
    
    # Token settings
    token_buffer_seconds: int = Field(
        default=300,  # Refresh token 5 minutes before expiry
        alias="KIOTVIET_TOKEN_BUFFER_SECONDS"
    )
    
    # Request settings
    request_timeout: int = Field(default=30, alias="KIOTVIET_REQUEST_TIMEOUT")
    max_retries: int = Field(default=3, alias="KIOTVIET_MAX_RETRIES")
    retry_delay: float = Field(default=1.0, alias="KIOTVIET_RETRY_DELAY")


def get_config() -> KiotVietConfig:
    """Get configuration instance."""
    return KiotVietConfig()
