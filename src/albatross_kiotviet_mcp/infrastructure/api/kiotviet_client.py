"""KiotViet API client implementation."""

import logging
from typing import Dict, Any, List, Optional
from .base_client import BaseKiotVietClient
from ..config.settings import KiotVietConfig

logger = logging.getLogger(__name__)


class KiotVietAPIClient(BaseKiotVietClient):
    """Client for interacting with KiotViet Public API.

    This class inherits all the common functionality (authentication, retry logic,
    error handling) from BaseKiotVietClient and only implements the specific
    API methods for KiotViet endpoints.
    """

    def __init__(self, config: KiotVietConfig):
        super().__init__(config)

    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Get product categories from KiotViet API.
        
        Args:
            page_size: Number of items per page (max 100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")
            hierarchical_data: Whether to return hierarchical structure
            
        Returns:
            API response containing categories data
        """
        data = {
            "pageSize": min(page_size, 100),  # Ensure max limit
            "currentItem": current_item,
            "orderDirection": order_direction,
            "hierachicalData": hierarchical_data
        }
        
        return await self.make_request("GET", "/categories", data=data)