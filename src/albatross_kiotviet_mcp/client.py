"""KiotViet API client - simplified implementation."""

import asyncio
import logging
from typing import Optional
import httpx
from pydantic import ValidationError

from .auth import TokenManager
from .config import KiotVietConfig
from .models import CategoryResponse

logger = logging.getLogger(__name__)


class KiotVietClient:
    """Simplified KiotViet API client with authentication and retry logic."""

    def __init__(self, config: KiotVietConfig):
        """Initialize the KiotViet API client.
        
        Args:
            config: KiotViet configuration
        """
        self.config = config
        self.token_manager = TokenManager(config)
        self._client: Optional[httpx.AsyncClient] = None

    async def __aenter__(self):
        """Async context manager entry."""
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.config.request_timeout),
            headers={"Content-Type": "application/json"}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        if self._client:
            try:
                await self._client.aclose()
            except Exception as e:
                logger.warning(f"Error closing HTTP client: {e}")
            finally:
                self._client = None

    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> CategoryResponse:
        """Get product categories from KiotViet API.

        Args:
            page_size: Number of items per page (1-100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")
            hierarchical_data: Whether to return hierarchical structure

        Returns:
            CategoryResponse with validated data

        Raises:
            ValueError: If parameters are invalid
            httpx.HTTPError: If API request fails
        """
        # Validate parameters
        self._validate_categories_params(page_size, current_item, order_direction, hierarchical_data)

        # Prepare request data
        data = {
            "pageSize": min(page_size, 100),
            "currentItem": current_item,
            "orderDirection": order_direction,
            "hierachicalData": hierarchical_data  # Note: KiotViet API uses this spelling
        }

        # Make API request with retry logic
        raw_response = await self._make_request("GET", "/categories", data=data)
        
        # Validate and return response
        try:
            return CategoryResponse.model_validate(raw_response)
        except ValidationError as e:
            logger.error(f"Invalid API response format: {e}")
            raise ValueError(f"API returned invalid data format: {str(e)}")

    def _validate_categories_params(
        self, 
        page_size: int, 
        current_item: int, 
        order_direction: str, 
        hierarchical_data: bool
    ) -> None:
        """Validate category request parameters."""
        if not isinstance(page_size, int) or page_size < 1 or page_size > 100:
            raise ValueError("page_size must be an integer between 1 and 100")
        
        if not isinstance(current_item, int) or current_item < 0:
            raise ValueError("current_item must be a non-negative integer")
        
        if order_direction not in ['Asc', 'Desc']:
            raise ValueError("order_direction must be 'Asc' or 'Desc'")
        
        if not isinstance(hierarchical_data, bool):
            raise ValueError("hierarchical_data must be a boolean")

    async def _make_request(self, method: str, endpoint: str, data: dict = None) -> dict:
        """Make HTTP request with authentication and retry logic."""
        if not self._client:
            raise RuntimeError("Client not initialized. Use async context manager.")

        url = f"{self.config.api_base_url}{endpoint}"
        
        for attempt in range(self.config.max_retries):
            try:
                # Get fresh token
                token = await self.token_manager.get_valid_token()
                headers = {"Authorization": f"Bearer {token}"}
                
                # Make request
                if method.upper() == "GET":
                    response = await self._client.get(url, params=data, headers=headers)
                else:
                    response = await self._client.request(method, url, json=data, headers=headers)
                
                # Handle authentication errors
                if response.status_code == 401:
                    logger.warning(f"Authentication failed, refreshing token (attempt {attempt + 1})")
                    await self.token_manager.refresh_token()
                    if attempt < self.config.max_retries - 1:
                        continue
                
                # Raise for HTTP errors
                response.raise_for_status()
                return response.json()
                
            except httpx.HTTPStatusError as e:
                logger.warning(f"HTTP error {e.response.status_code} for {method} {url} (attempt {attempt + 1})")
                
                # Retry server errors (5xx) and other retryable errors
                if attempt == self.config.max_retries - 1:
                    raise
                
                # Exponential backoff
                wait_time = (2 ** attempt) * self.config.retry_delay
                logger.info(f"Retrying in {wait_time}s...")
                await asyncio.sleep(wait_time)
                
            except httpx.RequestError as e:
                logger.warning(f"Request error for {method} {url}: {e}")
                
                if attempt == self.config.max_retries - 1:
                    raise
                
                # Exponential backoff
                wait_time = (2 ** attempt) * self.config.retry_delay
                logger.info(f"Retrying in {wait_time}s...")
                await asyncio.sleep(wait_time)
        
        # This should never be reached due to the raise statements above
        raise RuntimeError("Max retries exceeded")
