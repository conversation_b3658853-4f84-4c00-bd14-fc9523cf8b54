# Architecture Documentation

## Tool Registration Refactor

This document explains the refactored tool registration architecture that eliminates code duplication and follows clean architecture principles.

## Previous Architecture Issues

### Problem: Duplicate Tool Definitions
- **Tool Classes**: Defined in `tools/` folder with business logic
- **FastMCP Decorators**: Redefined in `server.py` with `@server.tool()` decorators
- **Result**: Code duplication, maintenance overhead, inconsistency

### Example of Old Duplication:
```python
# In tools/categories/categories_tool.py
class CategoriesTool(BaseMCPTool):
    def __init__(self, api_client):
        super().__init__(
            name="get_categories",
            description="Get product categories..."
        )

# In server.py (DUPLICATE!)
@server.tool(
    name="get_categories", 
    description="Get product categories..."
)
async def get_categories(...):
    tool = get_categories_tool()
    return await tool.execute(...)
```

## New Architecture: Auto-Registration

### Solution: Single Source of Truth
- **Tool Classes**: Define business logic AND FastMCP integration
- **Auto-Registration**: Programmatically register tools from classes
- **Result**: No duplication, clean separation of concerns

## Implementation Details

### 1. Enhanced Base Tool Class

```python
class BaseMCPTool(ABC):
    def get_wrapper_function(self) -> Callable:
        """Get a wrapper function for FastMCP registration."""
        return self._create_wrapper_function()
    
    def _create_wrapper_function(self) -> Callable:
        """Override in subclasses to provide proper signature."""
        # Subclasses implement this with correct parameters
```

### 2. Tool-Specific Wrapper Functions

Each tool class implements `_create_wrapper_function()`:

```python
class CategoriesTool(BaseMCPTool):
    def _create_wrapper_function(self) -> Callable:
        async def get_categories(
            page_size: int = 50,
            current_item: int = 0,
            order_direction: str = "Asc",
            hierarchical_data: bool = False
        ) -> Dict[str, Any]:
            """Get product categories from KiotViet API."""
            return await self.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )
        return get_categories
```

### 3. Auto-Registration System

```python
def register_tools():
    """Register all MCP tools with the FastMCP server."""
    # Get tool instances
    categories_tool = get_categories_tool()
    products_tool = get_products_tool()
    branches_tool = get_branches_tool()
    
    # Register using wrapper functions
    server.tool(
        name=categories_tool.name,
        description=categories_tool.description
    )(categories_tool.get_wrapper_function())
    
    # ... repeat for other tools
```

## Benefits

### ✅ Eliminated Code Duplication
- Tool name, description, and parameters defined once
- No more maintaining two versions of the same information

### ✅ Clean Architecture Maintained
- Business logic stays in tool classes
- FastMCP integration handled cleanly
- Separation of concerns preserved

### ✅ Type Safety & Introspection
- FastMCP can introspect function signatures
- Proper type hints and validation
- IDE support and autocompletion

### ✅ Easy Maintenance
- Add new tools by creating tool class only
- Modify parameters in one place
- Consistent error handling

### ✅ Testability
- Tool classes can be tested independently
- Mock dependencies easily
- Clear separation of concerns

## Adding New Tools

To add a new tool (e.g., `CustomersTool`):

1. **Create Tool Class**:
```python
class CustomersTool(BaseMCPTool):
    def __init__(self, api_client):
        super().__init__(
            name="get_customers",
            description="Get customers from KiotViet API"
        )
    
    async def execute(self, **kwargs):
        # Business logic here
    
    def _create_wrapper_function(self):
        async def get_customers(name: str = None):
            return await self.execute(name=name)
        return get_customers
```

2. **Add to Server**:
```python
def get_customers_tool() -> CustomersTool:
    # Singleton pattern
    
def register_tools():
    # Add to registration
    customers_tool = get_customers_tool()
    server.tool(
        name=customers_tool.name,
        description=customers_tool.description
    )(customers_tool.get_wrapper_function())
```

## Migration Summary

| Aspect | Before | After |
|--------|--------|-------|
| **Tool Definition** | Duplicated (class + decorator) | Single source (class only) |
| **Registration** | Manual decorators | Auto-registration |
| **Maintenance** | Update 2 places | Update 1 place |
| **Type Safety** | Partial | Full |
| **Testing** | Complex | Simple |
| **Architecture** | Mixed concerns | Clean separation |

This refactor maintains all existing functionality while significantly improving code quality and maintainability.
